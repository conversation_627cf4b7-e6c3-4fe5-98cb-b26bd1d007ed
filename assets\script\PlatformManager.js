cc.Class({
    extends: cc.Component,

    properties: {
        // ===== 基础配置 =====
        // 地面移动速度，将由GameManager控制
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },

        // 地面间距配置
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },

        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },

        // ===== 预制体引用 =====
        // 安全地面预制体
        safePlatformPrefab: {
            default: null,
            type: cc.Prefab,
            tooltip: "安全地面预制体，保证玩家可以安全跳跃"
        },

        // 挑战地面预制体数组
        challengePlatformPrefabs: {
            default: [],
            type: [cc.Prefab],
            tooltip: "挑战地面预制体数组"
        },

        // ===== 生成规则配置 =====
        // 安全地面生成频率（每隔几个平台强制生成一个安全平台）
        safeFrequency: {
            default: 5,
            tooltip: "每隔几个平台强制生成一个安全平台"
        },

        // 预生成距离，屏幕宽度的倍数
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;

        // 初始化状态标记
        this.isUsingPrefabs = false; // 是否已经切换到预制体生成模式
        this.platformCounter = 0;    // 生成的平台计数器，用于控制安全平台频率
        this.lastGenerateTime = 0;   // 上次生成地面的时间，用于限制生成频率
        this.lastPresetRightX = 0;   // 记录最后一个预设地面的右边缘位置

        // 初始化数组
        this.presetPlatforms = [];   // 预设地面数组（场景中的Ground节点）
        this.prefabPlatforms = [];   // 预制体地面数组（动态生成的）
        this.platformPool = [];      // 地面对象池，用于回收重用

        // 获取预设地面
        this.initPresetPlatforms();

    },

    // 获取场景中的预设地面
    initPresetPlatforms() {
        // 获取所有预设地面子节点
        this.presetPlatforms = this.node.children.filter(child =>
            child.name === 'SafePlatform' || child.name.startsWith('ChallengePlatform'));

        // 为每个预设地面添加标记
        this.presetPlatforms.forEach(platform => {
            platform._isPreset = true;
            platform._isActive = true;
        });

        // 计算预设地面的最右边缘位置
        this.updateLastPresetRightX();

        console.log(`找到 ${this.presetPlatforms.length} 个预设地面`);
    },

    // 更新最后一个预设地面的右边缘位置
    updateLastPresetRightX() {
        let rightmostX = -Infinity;
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                if (rightEdge > rightmostX) {
                    rightmostX = rightEdge;
                }
            }
        });
        if (rightmostX !== -Infinity) {
            this.lastPresetRightX = rightmostX;
        }
    },





    start() {
        // 预设地面已在onLoad中初始化，无需重复
    },

    update(dt) {
        // 移动所有活跃的地面
        this.moveAllPlatforms(dt);

        // 检查是否需要生成新地面
        this.checkAndGeneratePlatforms();

        // 回收移出屏幕的地面
        this.recyclePlatforms();
    },

    // 移动所有地面
    moveAllPlatforms(dt) {
        // 移动预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });

        // 移动预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    // 检查并生成新地面
    checkAndGeneratePlatforms() {
        // 只有当预设地面用完后才开始生成随机地面
        if (!this.isUsingPrefabs) {
            // 检查是否还有活跃的预设地面
            let hasActivePreset = this.presetPlatforms.some(platform => platform._isActive);
            if (!hasActivePreset) {
                this.isUsingPrefabs = true;
                console.log("预设地面已用完，开始生成随机地面");
            }
        }

        // 如果已经在使用预制体模式，检查是否需要生成新地面
        if (this.isUsingPrefabs) {
            let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;
            let currentRightmostX = this.getRightmostPlatformX();

            if (currentRightmostX < targetRightEdge) {
                let now = Date.now();
                if (this.prefabPlatforms.length < 20 && (!this.lastGenerateTime || (now - this.lastGenerateTime) > 50)) {
                    this.generatePrefabPlatform();
                    this.lastGenerateTime = now;
                }
            }
        }
    },

    // 生成预制体地面
    generatePrefabPlatform() {


        // 获取当前最右边的地面位置
        let currentRightmostX = this.getRightmostPlatformX();

        // 决定生成安全地面还是挑战地面
        let shouldGenerateSafe = this.shouldGenerateSafePlatform();
        let prefabToUse = shouldGenerateSafe ? this.safePlatformPrefab : this.getRandomChallengePrefab();

        // 尝试从对象池获取，如果没有则实例化新的
        let newPlatform = this.getPlatformFromPool();
        if (!newPlatform) {
            newPlatform = cc.instantiate(prefabToUse);
        }

        // 设置父节点
        newPlatform.parent = this.node;

        // 计算位置
        let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
        let newX = currentRightmostX + gap + newPlatform.width / 2;
        let newY = this.calculatePlatformY(shouldGenerateSafe);

        // 设置位置
        newPlatform.x = newX;
        newPlatform.y = newY;

        // 添加标记
        newPlatform._isPreset = false;
        newPlatform._isActive = true;
        newPlatform._isSafe = shouldGenerateSafe;


        // 设置地面分组（确保与预设地面使用相同分组）
        newPlatform.group = 'Ground';

        // 添加到预制体地面数组
        this.prefabPlatforms.push(newPlatform);

        // 更新计数器
        this.platformCounter++;

        // 只在出现错误时输出调试信息
        if (newX === undefined || newY === undefined) {
            console.error(`❌ 平台生成错误: newX=${newX}, newY=${newY}`);
        }
    },

    // 判断是否应该生成安全地面
    shouldGenerateSafePlatform() {
        // 每隔 safeFrequency 个平台强制生成一个安全平台
        return (this.platformCounter % this.safeFrequency === 0);
    },

    // 获取随机的挑战地面预制体
    getRandomChallengePrefab() {
        let randomIndex = Math.floor(Math.random() * this.challengePlatformPrefabs.length);
        return this.challengePlatformPrefabs[randomIndex];
    },

    // 计算地面的Y位置
    calculatePlatformY(isSafe) {
        if (isSafe) {
            // 安全地面：高度变化较小，确保玩家容易跳到
            let safeHeights = [-130, -110, -90, -70, -50]; // 相对安全的高度，稍微扩展范围
            return safeHeights[Math.floor(Math.random() * safeHeights.length)];
        } else {
            // 挑战地面：可以有更大的高度变化，从-200到100
            let challengeHeights = [-200, -170, -150, -130, -100, -70, -40, -10, 20, 50, 80, 100]; // 大幅扩展高度范围
            return challengeHeights[Math.floor(Math.random() * challengeHeights.length)];
        }
    },

    // 从对象池获取地面
    getPlatformFromPool() {
        // 简单实现：从池中取第一个可用的
        if (this.platformPool.length > 0) {
            let platform = this.platformPool.pop();
            platform.active = true; // 重新激活
            return platform;
        }
        return null;
    },

    // 回收移出屏幕的地面
    recyclePlatforms() {
        let leftBoundary = -this.screenWidth / 2 - 100; // 屏幕左边界外100像素

        // 检查预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                // 在预设地面移出屏幕前，更新最右边缘位置
                this.updateLastPresetRightX();
                // 预设地面移出屏幕后，标记为非活跃，不再重用
                platform._isActive = false;
            }
        });

        // 检查预制体地面的回收
        for (let i = this.prefabPlatforms.length - 1; i >= 0; i--) {
            let platform = this.prefabPlatforms[i];
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                // 预制体地面移出屏幕后，回收到对象池
                platform._isActive = false;
                platform.active = false; // 隐藏节点

                // 移到对象池
                this.platformPool.push(platform);
                this.prefabPlatforms.splice(i, 1);
            }
        }
    },

    // 获取当前最右边的活跃地面位置
    getRightmostPlatformX() {
        let rightmostX = -Infinity;

        // 检查预设地面
        let activePresetCount = 0;
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                activePresetCount++;
                let rightEdge = platform.x + platform.width / 2;
                if (rightEdge > rightmostX) {
                    rightmostX = rightEdge;
                }
            }
        });

        // 检查预制体地面
        let activePrefabCount = 0;
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                activePrefabCount++;
                let rightEdge = platform.x + platform.width / 2;
                if (rightEdge > rightmostX) {
                    rightmostX = rightEdge;
                }
            }
        });

        // 如果没有找到任何活跃地面，返回一个默认值
        if (rightmostX === -Infinity) {
            // 使用预设地面的最右边缘，或者一个默认起始位置
            if (this.presetRightmostX !== undefined) {
                rightmostX = this.presetRightmostX;
            } else {
                rightmostX = 200; // 默认起始位置
            }
        }

        return rightmostX;
    },



    // ===== 公共接口方法 =====

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    // 重置所有平台位置
    resetPlatforms() {
        // 重置状态标记
        this.isUsingPrefabs = false;
        this.platformCounter = 0;

        // 重置预设地面为活跃状态
        this.presetPlatforms.forEach(platform => {
            platform._isActive = true;
        });

        // 清理所有预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.prefabPlatforms = [];

        // 清理对象池
        this.platformPool.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.platformPool = [];
    },

});