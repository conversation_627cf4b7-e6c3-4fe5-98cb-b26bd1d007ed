cc.Class({
    extends: cc.Component,

    properties: {
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },
        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },
        safePlatformPrefab: {
            default: null,
            type: cc.Prefab,
            tooltip: "安全地面预制体"
        },
        challengePlatformPrefabs: {
            default: [],
            type: [cc.Prefab],
            tooltip: "挑战地面预制体数组"
        },
        safeFrequency: {
            default: 5,
            tooltip: "每隔几个平台强制生成一个安全平台"
        },
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        // 获取屏幕宽度，用于计算生成边界
        this.screenWidth = cc.winSize.width;

        // 初始化计数器和状态变量
        this.platformCounter = 0;        // 已生成的平台数量
        this.lastGenerateTime = 0;       // 上次生成时间，防止过度生成
        this.startGenerateX = 0;         // 预制体开始生成的X位置

        // 初始化地面相关数组
        this.safePlatform = null;        // 预设的安全地面
        this.prefabPlatforms = [];       // 动态生成的预制体地面数组
        this.platformPool = [];          // 地面对象池，用于回收重用

        // 查找并初始化预设的安全地面
        this.initSafePlatform();
    },

    // 查找场景中的SafePlatform节点，并计算预制体生成起始位置
    initSafePlatform() {
        this.safePlatform = this.node.getChildByName('SafePlatform');
        if (this.safePlatform) {
            // 计算预制体生成起始位置：SafePlatform右边缘 + 最小间距
            this.startGenerateX = this.safePlatform.x + this.safePlatform.width / 2 + this.minGapX;
            console.log(`找到SafePlatform，位置: (${this.safePlatform.x}, ${this.safePlatform.y})`);
        } else {
            console.error('未找到SafePlatform节点！');
            this.startGenerateX = 200; // 如果没找到，使用默认位置
        }
    },

    start() {},

    update(dt) {
        // 每帧执行的主要逻辑
        this.moveAllPlatforms(dt);          // 移动所有地面
        this.checkAndGeneratePlatforms();   // 检查是否需要生成新地面
        this.recyclePlatforms();            // 回收移出屏幕的地面
    },

    // 移动所有地面（包括预设地面和预制体地面）
    moveAllPlatforms(dt) {
        // 移动预设的安全地面
        if (this.safePlatform) {
            this.safePlatform.x -= this.moveSpeed * dt;
        }

        // 移动所有动态生成的预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    // 检查是否需要生成新的地面
    checkAndGeneratePlatforms() {
        // 计算目标右边界：屏幕右边缘 + 预生成距离
        let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;
        let currentRightmostX = this.getRightmostPlatformX();

        // 如果最右边的地面距离不够远，就生成新地面
        if (currentRightmostX < targetRightEdge) {
            let now = Date.now();
            // 限制生成频率和数量，避免过度生成
            if (this.prefabPlatforms.length < 20 && (!this.lastGenerateTime || (now - this.lastGenerateTime) > 50)) {
                this.generatePrefabPlatform();
                this.lastGenerateTime = now;
            }
        }
    },

    // 生成一个新的预制体地面
    generatePrefabPlatform() {
        // 获取当前最右边地面的位置
        let currentRightmostX = this.getRightmostPlatformX();

        // 决定生成安全地面还是挑战地面
        let shouldGenerateSafe = this.shouldGenerateSafePlatform();
        let prefabToUse = shouldGenerateSafe ? this.safePlatformPrefab : this.getRandomChallengePrefab();

        // 尝试从对象池获取地面，如果没有就创建新的
        let newPlatform = this.getPlatformFromPool();
        if (!newPlatform) {
            newPlatform = cc.instantiate(prefabToUse);
        }

        // 设置地面的父节点和位置
        newPlatform.parent = this.node;
        let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX); // 随机间距
        let newX = currentRightmostX + gap + newPlatform.width / 2;             // X位置
        let newY = this.calculatePlatformY(shouldGenerateSafe);                 // Y位置

        // 应用位置和属性
        newPlatform.x = newX;
        newPlatform.y = newY;
        newPlatform._isPreset = false;        // 标记为非预设地面
        newPlatform._isActive = true;         // 标记为活跃状态
        newPlatform._isSafe = shouldGenerateSafe; // 标记安全类型
        newPlatform.group = 'Ground';         // 设置碰撞分组

        // 添加到地面数组并更新计数器
        this.prefabPlatforms.push(newPlatform);
        this.platformCounter++;
    },

    // 判断是否应该生成安全地面（每隔几个平台强制生成一个安全平台）
    shouldGenerateSafePlatform() {
        return (this.platformCounter % this.safeFrequency === 0);
    },

    // 从挑战地面预制体数组中随机选择一个
    getRandomChallengePrefab() {
        let randomIndex = Math.floor(Math.random() * this.challengePlatformPrefabs.length);
        return this.challengePlatformPrefabs[randomIndex];
    },

    // 根据地面类型计算Y位置（高度）
    calculatePlatformY(isSafe) {
        if (isSafe) {
            // 安全地面：高度变化较小，容易跳到
            let safeHeights = [-130, -110, -90, -70, -50];
            return safeHeights[Math.floor(Math.random() * safeHeights.length)];
        } else {
            // 挑战地面：高度变化较大，增加难度
            let challengeHeights = [-200, -170, -150, -130, -100, -70, -40, -10, 20, 50, 80, 100];
            return challengeHeights[Math.floor(Math.random() * challengeHeights.length)];
        }
    },

    // 从对象池中获取可重用的地面节点
    getPlatformFromPool() {
        if (this.platformPool.length > 0) {
            let platform = this.platformPool.pop();
            platform.active = true; // 重新激活节点
            return platform;
        }
        return null; // 对象池为空，需要创建新节点
    },

    // 回收移出屏幕的地面到对象池
    recyclePlatforms() {
        let leftBoundary = -this.screenWidth / 2 - 100; // 屏幕左边界外100像素

        // 从后往前遍历，避免删除元素时索引错乱
        for (let i = this.prefabPlatforms.length - 1; i >= 0; i--) {
            let platform = this.prefabPlatforms[i];
            // 如果地面移出了屏幕左边界
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                platform._isActive = false;    // 标记为非活跃
                platform.active = false;       // 隐藏节点
                this.platformPool.push(platform); // 放入对象池重用
                this.prefabPlatforms.splice(i, 1); // 从活跃数组中移除
            }
        }
    },

    // 获取当前最右边地面的X位置
    getRightmostPlatformX() {
        let rightmostX = -Infinity;

        // 检查预设安全地面
        if (this.safePlatform) {
            let rightEdge = this.safePlatform.x + this.safePlatform.width / 2;
            rightmostX = Math.max(rightmostX, rightEdge);
        }

        // 检查所有活跃的预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                rightmostX = Math.max(rightmostX, rightEdge);
            }
        });

        // 如果没有找到任何地面，使用起始生成位置
        if (rightmostX === -Infinity) {
            rightmostX = this.startGenerateX || 200;
        }

        return rightmostX;
    },

    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    resetPlatforms() {
        this.platformCounter = 0;
        if (this.safePlatform) {
            this.safePlatform.x = 115;
        }
        this.prefabPlatforms.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.prefabPlatforms = [];
        this.platformPool.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.platformPool = [];
    },

});