cc.Class({
    extends: cc.Component,

    properties: {
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },
        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },
        safePlatformPrefab: {
            default: null,
            type: cc.Prefab,
            tooltip: "安全地面预制体"
        },
        challengePlatformPrefabs: {
            default: [],
            type: [cc.Prefab],
            tooltip: "挑战地面预制体数组"
        },
        safeFrequency: {
            default: 5,
            tooltip: "每隔几个平台强制生成一个安全平台"
        },
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        this.screenWidth = cc.winSize.width;
        this.platformCounter = 0;
        this.lastGenerateTime = 0;
        this.startGenerateX = 0;
        this.safePlatform = null;
        this.prefabPlatforms = [];
        this.platformPool = [];
        this.initSafePlatform();
    },

    initSafePlatform() {
        this.safePlatform = this.node.getChildByName('SafePlatform');
        if (this.safePlatform) {
            this.startGenerateX = this.safePlatform.x + this.safePlatform.width / 2 + this.minGapX;
            console.log(`找到SafePlatform，位置: (${this.safePlatform.x}, ${this.safePlatform.y})`);
        } else {
            console.error('未找到SafePlatform节点！');
            this.startGenerateX = 200;
        }
    },

    start() {},

    update(dt) {
        this.moveAllPlatforms(dt);
        this.checkAndGeneratePlatforms();
        this.recyclePlatforms();
    },

    moveAllPlatforms(dt) {
        if (this.safePlatform) {
            this.safePlatform.x -= this.moveSpeed * dt;
        }
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    checkAndGeneratePlatforms() {
        let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;
        let currentRightmostX = this.getRightmostPlatformX();
        if (currentRightmostX < targetRightEdge) {
            let now = Date.now();
            if (this.prefabPlatforms.length < 20 && (!this.lastGenerateTime || (now - this.lastGenerateTime) > 50)) {
                this.generatePrefabPlatform();
                this.lastGenerateTime = now;
            }
        }
    },

    generatePrefabPlatform() {
        let currentRightmostX = this.getRightmostPlatformX();
        let shouldGenerateSafe = this.shouldGenerateSafePlatform();
        let prefabToUse = shouldGenerateSafe ? this.safePlatformPrefab : this.getRandomChallengePrefab();

        let newPlatform = this.getPlatformFromPool();
        if (!newPlatform) {
            newPlatform = cc.instantiate(prefabToUse);
        }

        newPlatform.parent = this.node;
        let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
        let newX = currentRightmostX + gap + newPlatform.width / 2;
        let newY = this.calculatePlatformY(shouldGenerateSafe);

        newPlatform.x = newX;
        newPlatform.y = newY;
        newPlatform._isPreset = false;
        newPlatform._isActive = true;
        newPlatform._isSafe = shouldGenerateSafe;
        newPlatform.group = 'Ground';

        this.prefabPlatforms.push(newPlatform);
        this.platformCounter++;
    },

    shouldGenerateSafePlatform() {
        return (this.platformCounter % this.safeFrequency === 0);
    },

    getRandomChallengePrefab() {
        let randomIndex = Math.floor(Math.random() * this.challengePlatformPrefabs.length);
        return this.challengePlatformPrefabs[randomIndex];
    },

    calculatePlatformY(isSafe) {
        if (isSafe) {
            let safeHeights = [-130, -110, -90, -70, -50];
            return safeHeights[Math.floor(Math.random() * safeHeights.length)];
        } else {
            let challengeHeights = [-200, -170, -150, -130, -100, -70, -40, -10, 20, 50, 80, 100];
            return challengeHeights[Math.floor(Math.random() * challengeHeights.length)];
        }
    },

    getPlatformFromPool() {
        if (this.platformPool.length > 0) {
            let platform = this.platformPool.pop();
            platform.active = true;
            return platform;
        }
        return null;
    },

    recyclePlatforms() {
        let leftBoundary = -this.screenWidth / 2 - 100;
        for (let i = this.prefabPlatforms.length - 1; i >= 0; i--) {
            let platform = this.prefabPlatforms[i];
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                platform._isActive = false;
                platform.active = false;
                this.platformPool.push(platform);
                this.prefabPlatforms.splice(i, 1);
            }
        }
    },

    getRightmostPlatformX() {
        let rightmostX = -Infinity;
        if (this.safePlatform) {
            let rightEdge = this.safePlatform.x + this.safePlatform.width / 2;
            rightmostX = Math.max(rightmostX, rightEdge);
        }
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                rightmostX = Math.max(rightmostX, rightEdge);
            }
        });
        if (rightmostX === -Infinity) {
            rightmostX = this.startGenerateX || 200;
        }
        return rightmostX;
    },



    // ===== 公共接口方法 =====

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    // 重置所有平台位置
    resetPlatforms() {
        // 重置状态标记
        this.platformCounter = 0;

        // 重置安全地面位置
        if (this.safePlatform) {
            this.safePlatform.x = 115; // 重置到初始位置
        }

        // 清理所有预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.prefabPlatforms = [];

        // 清理对象池
        this.platformPool.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.platformPool = [];
    },

});